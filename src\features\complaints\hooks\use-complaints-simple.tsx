import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { useUserWithProfile } from '@/hooks/use-auth';
import { CreateComplaintInput } from '../schemas';
import { toast } from 'sonner';
import type { Database } from '@/types/database';

type Complaint = Database['public']['Tables']['complaints']['Row'];
type ComplaintInsert = Database['public']['Tables']['complaints']['Insert'];

// Hook to fetch all complaints
export function useComplaints() {
  return useQuery({
    queryKey: ['complaints'],
    queryFn: async (): Promise<Complaint[]> => {
      const { data, error } = await supabase
        .from('complaints')
        .select('*')
        .is('deleted_at', null)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to create a new complaint
export function useCreateComplaint() {
  const queryClient = useQueryClient();
  const { data: user } = useUserWithProfile();

  return useMutation({
    mutationFn: async (
      data: CreateComplaintInput & { proofOfRepairFiles?: File[] },
    ) => {
      if (!user?.id) {
        throw new Error('User must be authenticated to create complaints');
      }

      // TODO: File upload handling will be implemented later
      // For now, we'll store file names as placeholder
      const proofFileUrls =
        data.proofOfRepairFiles?.map((file) => file.name) || [];

      // Generate complaint number
      const year = new Date().getFullYear();
      const complaintNumber = `DCL-${year}-${String(Math.floor(Math.random() * 9999) + 1).padStart(4, '0')}`; // Map UI status to database status
      const mapStatusToDb = (
        uiStatus?: string,
      ): Database['public']['Enums']['complaint_status'] => {
        switch (uiStatus) {
          case 'complete':
            return 'closed';
          case 'in progress':
            return 'on_hold';
          case 'pending':
          default:
            return 'open';
        }
      };

      const complaintData: ComplaintInsert = {
        email: data.email || '<EMAIL>',
        number: complaintNumber,
        damage_complaint_date:
          data.damageComplaintDate?.toISOString().split('T')[0] ||
          new Date().toISOString().split('T')[0],
        contractor_company_name:
          data.contractorCompanyName || 'Unknown Company',
        location: data.location || 'Unknown Location',
        no_pma_lif: data.noPmaLif || 'N/A',
        damage_complaint_description:
          data.description || 'No description provided',
        expected_completion_date:
          data.expectedCompletionDate?.toISOString().split('T')[0] ||
          new Date().toISOString().split('T')[0],
        involves_mantrap: false, // TODO: Add this field to the form
        status: mapStatusToDb(data.status),
        created_by: user.id,
        created_at: new Date().toISOString(),
        // TODO: Map other fields to database schema
        // These fields need to be connected to proper relations:
        // agency_id: will need to map from agency string
        // contractor_company_id: will need to map from contractor company name
        // lift_id: will need to map from location/lift information
      };

      const { data: result, error } = await supabase
        .from('complaints')
        .insert(complaintData)
        .select()
        .single();

      if (error) throw error;

      return {
        ...result,
        complaintNumber,
        proofFileUrls,
      };
    },
    onSuccess: (data) => {
      // Invalidate and refetch complaints
      queryClient.invalidateQueries({ queryKey: ['complaints'] });
      toast.success('Complaint created successfully!');
      console.log('Complaint created successfully:', data);
    },
    onError: (error: Error) => {
      console.error('Failed to create complaint:', error);
      toast.error(`Failed to create complaint: ${error.message}`);
    },
  });
}

// Hook to update an existing complaint
export function useUpdateComplaint() {
  const queryClient = useQueryClient();
  const { data: user } = useUserWithProfile();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: CreateComplaintInput & { proofOfRepairFiles?: File[] };
    }) => {
      if (!user?.id) {
        throw new Error('User must be authenticated to update complaints');
      }

      // TODO: File upload handling will be implemented later
      // For now, we'll store file names as placeholder
      const proofFileUrls =
        data.proofOfRepairFiles?.map((file) => file.name) || [];

      // Map UI status to database status
      const mapStatusToDb = (
        uiStatus?: string,
      ): Database['public']['Enums']['complaint_status'] => {
        switch (uiStatus) {
          case 'complete':
            return 'closed';
          case 'in progress':
            return 'on_hold';
          case 'pending':
          default:
            return 'open';
        }
      };

      const complaintData = {
        damage_complaint_description:
          data.description || 'No description provided',
        status: mapStatusToDb(data.status),
        updated_at: new Date().toISOString(),
        // TODO: Map other fields to database schema when they're available
      };

      const { data: result, error } = await supabase
        .from('complaints')
        .update(complaintData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      return {
        ...result,
        proofFileUrls,
      };
    },
    onSuccess: (data) => {
      // Invalidate and refetch complaints
      queryClient.invalidateQueries({ queryKey: ['complaints'] });
      toast.success('Complaint updated successfully!');
      console.log('Complaint updated successfully:', data);
    },
    onError: (error: Error) => {
      console.error('Failed to update complaint:', error);
      toast.error(`Failed to update complaint: ${error.message}`);
    },
  });
}
