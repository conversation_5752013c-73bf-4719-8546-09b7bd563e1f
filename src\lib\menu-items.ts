import {
  Home,
  Settings,
  User,
  BarChart3,
  FileText,
  Building,
  Users,
  UserCheck,
  ArrowUpDown,
  FileSignature,
  Calendar,
  Shield,
  MessageSquare,
  UserX,
  Building2,
} from 'lucide-react';
import type { PermissionMenuitem } from '@/types/rbac';

// Lift Management System menu items with their permission requirements
export const MENU_ITEMS: PermissionMenuitem[] = [
  {
    title: 'dashboard',
    url: '/dashboard',
    icon: Home,
    permission: 'dashboard.view',
    roles: ['JKR', 'Contractor'],
    description: 'descriptions.dashboard',
  },
  {
    title: 'lifts',
    url: '/lifts',
    icon: ArrowUpDown,
    permission: 'lifts.view',
    roles: ['JKR', 'Contractor'],
    description: 'descriptions.lifts',
  },
  {
    title: 'buildings',
    url: '/buildings',
    icon: Building2,
    permission: 'buildings.view',
    roles: ['JKR', 'Contractor'],
    description: 'descriptions.buildings',
  },
  {
    title: 'contracts',
    url: '/contracts',
    icon: FileSignature,
    permission: 'contracts.view',
    roles: ['JK<PERSON>', 'Contractor'],
    description: 'descriptions.contracts',
  },
  {
    title: 'dailyLogs',
    url: '/daily-logs',
    icon: Calendar,
    permission: 'daily_logs.view',
    roles: ['JKR', 'Contractor', 'Client'],
    description: 'descriptions.dailyLogs',
  },
  {
    title: 'pmas',
    url: '/pmas',
    icon: Shield,
    permission: 'pmas.view',
    roles: ['JKR', 'Contractor'],
    description: 'descriptions.pmas',
  },
  {
    title: 'complaints',
    url: '/complaints',
    icon: MessageSquare,
    permission: 'complaints.view',
    roles: ['JKR', 'Contractor', 'Client'],
    description: 'descriptions.complaints',
  },
  {
    title: 'contractors',
    url: '/contractors',
    icon: UserCheck,
    permission: 'contractors.view',
    roles: ['JKR'],
    description: 'descriptions.contractors',
  },
  {
    title: 'clients',
    url: '/clients',
    icon: Building,
    permission: 'clients.view',
    roles: ['JKR', 'Contractor'],
    description: 'descriptions.clients',
  },
  {
    title: 'blacklist',
    url: '/blacklist',
    icon: UserX,
    permission: 'blacklist.view',
    roles: ['JKR'],
    description: 'descriptions.blacklist',
  },
  {
    title: 'analytics',
    url: '/analytics',
    icon: BarChart3,
    permission: 'analytics.view',
    roles: ['JKR', 'Contractor'],
    description: 'descriptions.analytics',
  },
  {
    title: 'reports',
    url: '/reports',
    icon: FileText,
    permission: 'reports.view',
    roles: ['JKR', 'Contractor'],
    description: 'descriptions.reports',
  },
  {
    title: 'users',
    url: '/users',
    icon: Users,
    permission: 'users.view',
    roles: ['JKR'],
    description: 'descriptions.users',
  },
  {
    title: 'profile',
    url: '/profile',
    icon: User,
    permission: 'profile.view',
    roles: ['JKR', 'Contractor', 'Client'],
    description: 'descriptions.profile',
  },
  {
    title: 'settings',
    url: '/settings',
    icon: Settings,
    permission: 'settings.view',
    roles: ['JKR'],
    description: 'descriptions.settings',
  },
];
