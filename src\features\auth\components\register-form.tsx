'use client';

import * as React from 'react';
import { useForm, type SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Info, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { useSignUp } from '../hooks/use-auth';
import { createRegisterSchema, type RegisterFormValues } from '../schemas';
import {
  useAuthTranslations,
  useValidationTranslations,
} from '@/hooks/use-translations';

interface RegisterFormProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
}

export function RegisterForm({ ...props }: RegisterFormProps) {
  const signUpMutation = useSignUp();
  const auth = useAuthTranslations();
  const validation = useValidationTranslations();

  // Use schema from schemas file
  const registerSchema = createRegisterSchema(validation, auth);
  const form = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      email: '',
      password: '',
      confirmPassword: '',
      role: undefined,
    },
  });
  const onSubmit: SubmitHandler<RegisterFormValues> = async (values) => {
    try {
      await signUpMutation.mutateAsync({
        email: values.email,
        password: values.password,
        role: values.role, // Now properly typed as UserRole
      });
      toast.success(auth('registerSuccess'));
    } catch (error: unknown) {
      console.error('Registration failed:', error);

      // Handle specific error cases
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      if (errorMessage?.includes('User already registered')) {
        toast.error(auth('userExists'));
      } else if (errorMessage?.includes('Profile creation error')) {
        toast.error(auth('profileCreationError'));
      } else if (errorMessage?.includes('email_address_invalid')) {
        toast.error(validation('email'));
      } else if (errorMessage?.includes('signup_disabled')) {
        toast.error(auth('signupDisabled'));
      } else {
        // Fallback for other errors
        toast.error(errorMessage || auth('registerFailed'));
      }
    }
  };

  const isLoading = signUpMutation.isPending;

  return (
    <div className={cn('flex flex-col gap-6')} {...props}>
      <div className="flex flex-col items-center gap-2 text-center">
        <h1 className="text-2xl font-bold">{auth('registerTitle')}</h1>
        <p className="text-muted-foreground text-sm text-balance">
          {auth('registerSubtitle')}
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {signUpMutation.error && (
            <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
              {signUpMutation.error.message}
            </div>
          )}

          <div className="grid gap-4">
            {/* Role Selection */}
            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {auth('role')} <span className="text-red-500">*</span>
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={auth('selectRole')} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="JKR">{auth('jkr')}</SelectItem>
                      <SelectItem value="Contractor">
                        {auth('contractor')}
                      </SelectItem>
                      <SelectItem value="Client">{auth('client')}</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />{' '}
            {/* Email Field */}
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {auth('email')} <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder={auth('emailPlaceholder')}
                      type="email"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      {auth('password')} <span className="text-red-500">*</span>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                        </TooltipTrigger>
                        <TooltipContent className="max-w-xs">
                          <p className="font-medium mb-2">
                            {auth('passwordRequirements')}
                          </p>
                          <ul className="text-sm space-y-1">
                            <li>• {auth('passwordReq1')}</li>
                            <li>• {auth('passwordReq2')}</li>
                            <li>• {auth('passwordReq3')}</li>
                            <li>• {auth('passwordReq4')}</li>
                            <li>• {auth('passwordReq5')}</li>
                          </ul>
                        </TooltipContent>
                      </Tooltip>
                    </FormLabel>
                    <FormControl>
                      <Input type="password" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {auth('confirmPassword')}{' '}
                      <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input type="password" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {auth('creatingAccount')}
                </>
              ) : (
                auth('createAccount')
              )}
            </Button>
            <div className="text-center text-sm">
              {auth('alreadyHaveAccount')}{' '}
              <Link href="/login" className="underline underline-offset-4">
                {auth('signIn')}
              </Link>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}
