import type { UserRole } from '@/types/auth';
import type {
  Permission,
  Resource,
  Action,
  RoutePermission,
} from '@/types/rbac';

// Role-based permissions mapping for Lift Management System
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  JKR: [
    // JKR (Jaba<PERSON>) - Full administrative access
    'dashboard.view',
    'analytics.view',
    'reports.view',
    'reports.create',
    'reports.manage',
    'profile.view',
    'profile.edit',
    'settings.view',
    'settings.manage',

    // Lift Management
    'lifts.view',
    'lifts.create',
    'lifts.edit',
    'lifts.delete',
    'lifts.assign',

    // Building Management
    'buildings.view',
    'buildings.create',
    'buildings.edit',
    'buildings.delete',

    // Contractor Management
    'contractors.view',
    'contractors.create',
    'contractors.edit',
    'contractors.delete',
    'contractors.activate',
    'contractors.deactivate',
    'contractors.blacklist',
    'contractors.approve',

    // Client Management
    'clients.view',
    'clients.create',
    'clients.edit',
    'clients.delete',

    // Contract Management
    'contracts.view',
    'contracts.create',
    'contracts.edit',
    'contracts.delete',
    'contracts.approve',

    // Daily Logs
    'daily_logs.view',
    'daily_logs.create',
    'daily_logs.edit',
    'daily_logs.delete',

    // PMA Management
    'pmas.view',
    'pmas.create',
    'pmas.edit',
    'pmas.approve',
    'pmas.reject',

    // Complaint Management
    'complaints.view',
    'complaints.create',
    'complaints.edit',
    'complaints.resolve',
    'complaints.assign',

    // Blacklist Management
    'blacklist.view',
    'blacklist.create',
    'blacklist.edit',
    'blacklist.delete',

    // User Management
    'users.view',
    'users.create',
    'users.edit',
    'users.delete',
    'users.activate',
    'users.deactivate',
  ],
  Contractor: [
    // Contractors - Service providers with operational access
    'dashboard.view',
    'analytics.view',
    'reports.view',
    'reports.create',
    'profile.view',
    'profile.edit',

    // View assigned lifts and buildings
    'lifts.view',
    'buildings.view',

    // View clients they serve
    'clients.view',

    // View their contracts
    'contracts.view',

    // Daily Logs - Full access for their assignments
    'daily_logs.view',
    'daily_logs.create',
    'daily_logs.edit',

    // PMA Submissions
    'pmas.view',
    'pmas.create',
    'pmas.edit',

    // Handle complaints for their lifts
    'complaints.view',
    'complaints.create',
    'complaints.edit',
  ],
  Client: [
    //Dashboard view
    'dashboard.view',

    // Clients - Building owners with limited access to daily logs and complaints only
    'profile.view',
    'profile.edit',

    // View daily logs for their lifts
    'daily_logs.view',

    // Submit and view complaints for their lifts
    'complaints.view',
    'complaints.create',
  ],
};

// Route-based permissions for Lift Management System
export const ROUTE_PERMISSIONS: RoutePermission[] = [
  // Core Pages
  {
    path: '/dashboard',
    permission: 'dashboard.view',
    roles: ['JKR', 'Contractor', 'Client'],
  },
  {
    path: '/analytics',
    permission: 'analytics.view',
    roles: ['JKR', 'Contractor'],
  },
  {
    path: '/reports',
    permission: 'reports.view',
    roles: ['JKR', 'Contractor'],
  },
  {
    path: '/profile',
    permission: 'profile.view',
    roles: ['JKR', 'Contractor', 'Client'],
  },
  { path: '/settings', permission: 'settings.view', roles: ['JKR'] },

  // Lift Management
  {
    path: '/lifts',
    permission: 'lifts.view',
    roles: ['JKR', 'Contractor'],
  },
  { path: '/lifts/create', permission: 'lifts.create', roles: ['JKR'] },
  { path: '/lifts/edit', permission: 'lifts.edit', roles: ['JKR'] },

  // Building Management
  {
    path: '/buildings',
    permission: 'buildings.view',
    roles: ['JKR', 'Contractor'],
  },
  { path: '/buildings/create', permission: 'buildings.create', roles: ['JKR'] },
  { path: '/buildings/edit', permission: 'buildings.edit', roles: ['JKR'] },

  // Contractor Management
  { path: '/contractors', permission: 'contractors.view', roles: ['JKR'] },
  {
    path: '/contractors/create',
    permission: 'contractors.create',
    roles: ['JKR'],
  },
  { path: '/contractors/edit', permission: 'contractors.edit', roles: ['JKR'] },

  // Client Management
  {
    path: '/clients',
    permission: 'clients.view',
    roles: ['JKR', 'Contractor'],
  },
  { path: '/clients/create', permission: 'clients.create', roles: ['JKR'] },
  { path: '/clients/edit', permission: 'clients.edit', roles: ['JKR'] },

  // Contract Management
  {
    path: '/contracts',
    permission: 'contracts.view',
    roles: ['JKR', 'Contractor'],
  },
  { path: '/contracts/create', permission: 'contracts.create', roles: ['JKR'] },
  { path: '/contracts/edit', permission: 'contracts.edit', roles: ['JKR'] },

  // Daily Logs
  {
    path: '/daily-logs',
    permission: 'daily_logs.view',
    roles: ['JKR', 'Contractor', 'Client'],
  },
  {
    path: '/daily-logs/create',
    permission: 'daily_logs.create',
    roles: ['JKR', 'Contractor'],
  },
  {
    path: '/daily-logs/edit',
    permission: 'daily_logs.edit',
    roles: ['JKR', 'Contractor'],
  },
  // PMA Management
  {
    path: '/pmas',
    permission: 'pmas.view',
    roles: ['JKR', 'Contractor'],
  },
  {
    path: '/pmas/create',
    permission: 'pmas.create',
    roles: ['JKR', 'Contractor'],
  },
  { path: '/pmas/edit', permission: 'pmas.edit', roles: ['JKR', 'Contractor'] },
  { path: '/pmas/approve', permission: 'pmas.approve', roles: ['JKR'] },

  // Complaint Management
  {
    path: '/complaints',
    permission: 'complaints.view',
    roles: ['JKR', 'Contractor', 'Client'],
  },
  {
    path: '/complaints/create',
    permission: 'complaints.create',
    roles: ['JKR', 'Contractor', 'Client'],
  },
  {
    path: '/complaints/edit',
    permission: 'complaints.edit',
    roles: ['JKR', 'Contractor'],
  },

  // Blacklist Management
  { path: '/blacklist', permission: 'blacklist.view', roles: ['JKR'] },
  { path: '/blacklist/create', permission: 'blacklist.create', roles: ['JKR'] },
  { path: '/blacklist/edit', permission: 'blacklist.edit', roles: ['JKR'] },

  // User Management
  { path: '/users', permission: 'users.view', roles: ['JKR'] },
  { path: '/users/create', permission: 'users.create', roles: ['JKR'] },
  { path: '/users/edit', permission: 'users.edit', roles: ['JKR'] },
];

/**
 * Check if a user role has a specific permission
 */
export function hasPermission(
  userRole: UserRole | null,
  permission: Permission,
): boolean {
  if (!userRole) return false;
  return ROLE_PERMISSIONS[userRole]?.includes(permission) || false;
}

/**
 * Check if a user role can access a specific route
 */
export function canAccessRoute(
  userRole: UserRole | null,
  path: string,
): boolean {
  if (!userRole) return false;

  const routePermission = ROUTE_PERMISSIONS.find(
    (route) => route.path === path,
  );
  if (!routePermission) return false;

  return routePermission.roles.includes(userRole);
}

/**
 * Get all permissions for a user role
 */
export function getUserPermissions(userRole: UserRole | null): Permission[] {
  if (!userRole) return [];
  return ROLE_PERMISSIONS[userRole] || [];
}

/**
 * Check if a user role can perform an action on a resource
 */
export function canPerformAction(
  userRole: UserRole | null,
  resource: Resource,
  action: Action,
): boolean {
  const permission = `${resource}.${action}` as Permission;
  return hasPermission(userRole, permission);
}

/**
 * Filter accessible routes for a user role
 */
export function getAccessibleRoutes(
  userRole: UserRole | null,
): RoutePermission[] {
  if (!userRole) return [];
  return ROUTE_PERMISSIONS.filter((route) => route.roles.includes(userRole));
}

/**
 * Get role hierarchy level (higher number = more permissions)
 */
export function getRoleLevel(role: UserRole): number {
  const hierarchy = {
    Client: 1,
    Contractor: 2,
    JKR: 3,
  };
  return hierarchy[role];
}

/**
 * Check if user role has higher or equal permissions than required role
 */
export function hasRoleOrHigher(
  userRole: UserRole | null,
  requiredRole: UserRole,
): boolean {
  if (!userRole) return false;
  return getRoleLevel(userRole) >= getRoleLevel(requiredRole);
}
