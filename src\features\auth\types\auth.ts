import type { Database } from '@/types/database';
import type { Session } from '@supabase/supabase-js';

// Database type aliases for easier use
export type UserRole = Database['public']['Enums']['user_role'];
export type Profile = Database['public']['Tables']['profiles']['Row'];
export type ProfileInsert = Database['public']['Tables']['profiles']['Insert'];
export type ProfileUpdate = Database['public']['Tables']['profiles']['Update'];

// Auth-specific interfaces that use database types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface SignUpCredentials {
  email: string;
  password: string;
  role: UserRole; // Uses database enum type
}

// Extended user type that includes profile data
export interface UserWithProfile {
  id: string;
  email: string | null;
  user_metadata: {
    full_name?: string;
    phone_number?: string;
    role?: UserRole;
  };
  profile?: Profile;
}

// Auth response types
export interface AuthResponse {
  user: UserWithProfile | null;
  session: Session | null; // Properly typed Supabase session
}

// Profile creation payload that matches database schema
export interface CreateProfilePayload {
  id: string;
  email: string;
  name: string;
  phone_number?: string | null;
  role: UserRole;
}

// Password reset payload
export interface PasswordResetPayload {
  email: string;
  redirectTo?: string;
}
