'use client';

import { useState } from 'react';
import { format } from 'date-fns';
import {
  MoreHorizontal,
  Eye,
  Edit,
  Search,
  Calendar,
  Building,
  MapPin,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { ComplaintUI, ComplaintTableProps } from '../types/ui-types';
import { useComplaints } from '../hooks/use-complaints-simple';

const STATUS_CONFIG = {
  pending: {
    label: 'Pending',
    variant: 'secondary' as const,
    icon: Clock,
    color: 'text-yellow-600',
  },
  in_progress: {
    label: 'In Progress',
    variant: 'default' as const,
    icon: AlertCircle,
    color: 'text-blue-600',
  },
  completed: {
    label: 'Completed',
    variant: 'default' as const,
    icon: CheckCircle,
    color: 'text-green-600',
  },
  cancelled: {
    label: 'Cancelled',
    variant: 'destructive' as const,
    icon: XCircle,
    color: 'text-red-600',
  },
} as const;

export function ComplaintsTable({
  onViewComplaint,
  onEditComplaint,
}: ComplaintTableProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Fetch real complaints data
  const { data: complaintsData = [], isLoading, error } = useComplaints(); // Convert database complaints to UI format
  const mapDbStatusToUI = (dbStatus: string): ComplaintUI['status'] => {
    switch (dbStatus) {
      case 'closed':
        return 'completed';
      case 'on_hold':
        return 'in_progress';
      case 'open':
      default:
        return 'pending';
    }
  };

  const complaints: ComplaintUI[] = complaintsData.map((complaint) => ({
    id: complaint.id,
    complaintNumber: `DCL-${new Date(complaint.created_at || '').getFullYear()}-${complaint.id.slice(-4)}`,
    email: complaint.email || 'N/A',
    damageComplaintDate:
      complaint.damage_complaint_date ||
      complaint.created_at ||
      new Date().toISOString(),
    expectedCompletionDate:
      complaint.expected_completion_date ||
      complaint.created_at ||
      new Date().toISOString(),
    agency: 'N/A', // TODO: Get from agency relation
    contractorCompanyName: complaint.contractor_company_name || 'N/A',
    location: complaint.location || 'N/A',
    noPmaLif: complaint.no_pma_lif || 'N/A',
    description: complaint.damage_complaint_description,
    status: mapDbStatusToUI(complaint.status),
    createdAt: complaint.created_at || new Date().toISOString(),
    // TODO: Map other fields when database schema is complete
  }));

  // Filter complaints based on search and status
  const filteredComplaints = complaints.filter((complaint) => {
    const matchesSearch =
      !searchTerm ||
      complaint.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      complaint.contractorCompanyName
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      complaint.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
      complaint.noPmaLif?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      complaint.description?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      statusFilter === 'all' || complaint.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Format date helper
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy');
    } catch {
      return dateString;
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Damage Complaint Logs
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center gap-2">
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
              Loading complaints...
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Show error state
  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Damage Complaint Logs
          </CardTitle>
        </CardHeader>{' '}
        <CardContent>
          <div className="flex flex-col items-center gap-2 py-8">
            <AlertCircle className="h-12 w-12 text-red-500" />
            <p className="text-red-600">Failed to load complaints</p>{' '}
            <p className="text-sm text-muted-foreground">
              {error instanceof Error
                ? error.message
                : 'Unknown error occurred'}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Render status badge
  const renderStatus = (status: ComplaintUI['status']) => {
    const config = STATUS_CONFIG[status];
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Damage Complaint Logs
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Filters and Search */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search by email, company, location, PMA LIF, or description..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9"
            />
          </div>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="in_progress">In Progress</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Email</TableHead>
                <TableHead>Complaint Date</TableHead>
                <TableHead>Expected Completion</TableHead>
                <TableHead>Agency</TableHead>
                <TableHead>Company/Contractor</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>NO PMA LIF</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="w-16">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredComplaints.length > 0 ? (
                filteredComplaints.map((complaint) => (
                  <TableRow
                    key={complaint.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => onViewComplaint(complaint)}
                  >
                    <TableCell className="font-medium">
                      {complaint.email}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        {formatDate(complaint.damageComplaintDate)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        {formatDate(complaint.expectedCompletionDate)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Building className="h-4 w-4 text-muted-foreground" />
                        <span className="truncate max-w-32">
                          {complaint.agency}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div
                        className="truncate max-w-48"
                        title={complaint.contractorCompanyName}
                      >
                        {complaint.contractorCompanyName}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <span
                          className="truncate max-w-32"
                          title={complaint.location}
                        >
                          {complaint.location}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <code className="text-xs bg-muted px-1 py-0.5 rounded">
                        {complaint.noPmaLif || 'N/A'}
                      </code>
                    </TableCell>
                    <TableCell>{renderStatus(complaint.status)}</TableCell>
                    <TableCell onClick={(e) => e.stopPropagation()}>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>{' '}
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => onViewComplaint(complaint)}
                            className="flex items-center gap-2"
                          >
                            <Eye className="h-4 w-4" />
                            View Details
                          </DropdownMenuItem>
                          {onEditComplaint && (
                            <DropdownMenuItem
                              onClick={() => onEditComplaint(complaint)}
                              className="flex items-center gap-2"
                            >
                              <Edit className="h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={9} className="text-center py-8">
                    <div className="flex flex-col items-center gap-2">
                      <FileText className="h-12 w-12 text-muted-foreground" />
                      <p className="text-muted-foreground">
                        {searchTerm || statusFilter !== 'all'
                          ? 'No complaints found matching your criteria'
                          : 'No damage complaint logs found'}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {searchTerm || statusFilter !== 'all'
                          ? 'Try adjusting your search or filters'
                          : 'Create your first complaint log to get started'}
                      </p>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {/* Stats */}
        <div className="flex items-center justify-between mt-4">
          <p className="text-sm text-muted-foreground">
            Showing {filteredComplaints.length} of {complaints.length}{' '}
            complaints
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
