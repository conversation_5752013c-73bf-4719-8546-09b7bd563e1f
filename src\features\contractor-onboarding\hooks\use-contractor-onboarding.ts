import { useState, useCallback, useEffect } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from 'sonner';
import { generateCompanyCode } from '@/lib/utils';
import {
  step1Schema,
  step2Schema,
  step3Schema,
  type ContractorStep1FormValues,
  type ContractorStep2FormValues,
  type ContractorStep3FormValues,
  type ContractorFullFormValues,
} from '../schemas/contractor-onboarding-schemas';

export interface UseContractorOnboardingProps {
  onSubmit?: (values: ContractorFullFormValues) => void | Promise<void>;
}

export function useContractorOnboarding({
  onSubmit,
}: UseContractorOnboardingProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [step1Data, setStep1Data] = useState<ContractorStep1FormValues | null>(
    null,
  );
  const [step2Data, setStep2Data] = useState<ContractorStep2FormValues | null>(
    null,
  );
  const [isCodeCopied, setIsCodeCopied] = useState<boolean>(false); // Form instances
  const step1Form = useForm<ContractorStep1FormValues>({
    resolver: zodResolver(step1Schema),
    defaultValues: {
      fullName: '',
      icNumber: '',
      phoneNumber: '',
      role: undefined,
    },
  });

  const step2Form = useForm<ContractorStep2FormValues>({
    resolver: zodResolver(step2Schema),
    defaultValues: {
      companyRegistrationType: 'create',
      specialCode: '',
    },
  });

  const step3Form = useForm<ContractorStep3FormValues>({
    resolver: zodResolver(step3Schema),
    defaultValues: {
      company_name: '',
      company_type: undefined,
      company_hotline: '',
      oem_name: '',
      code: '',
    },
  });

  // Watch values
  const companyRegistrationType = step2Form.watch('companyRegistrationType');
  const watchedCompanyType = step3Form.watch('company_type');

  // Initialize company code when step 3 is reached
  useEffect(() => {
    if (currentStep === 3 && !step3Form.getValues('code')) {
      step3Form.setValue('code', generateCompanyCode());
    }
  }, [currentStep, step3Form]);

  // Step submit handlers
  const handleStep1Submit: SubmitHandler<ContractorStep1FormValues> =
    useCallback(async (values) => {
      setStep1Data(values);
      setCurrentStep(2);
      toast.success('Personal information saved!');
    }, []);

  const handleStep2Submit: SubmitHandler<ContractorStep2FormValues> =
    useCallback(
      async (values) => {
        if (!step1Data) {
          toast.error('Please complete step 1 first');
          setCurrentStep(1);
          return;
        }

        setStep2Data(values);

        try {
          if (values.companyRegistrationType === 'create') {
            // Move to step 3 for company creation
            setCurrentStep(3);
            toast.success('Proceeding to company creation...');
          } else if (values.companyRegistrationType === 'join') {
            // Handle joining existing company - complete the flow
            const fullData: ContractorFullFormValues = {
              ...step1Data,
              ...values,
            };
            if (onSubmit) {
              await onSubmit(fullData);
            } else {
              console.log('Joining company with code:', values.specialCode);
              toast.success('Successfully joined the company!');
            }
          }
        } catch (error) {
          const errorMessage =
            error instanceof Error
              ? error.message
              : 'An error occurred. Please try again.';
          toast.error(errorMessage);
        }
      },
      [step1Data, onSubmit],
    );

  const handleStep3Submit: SubmitHandler<ContractorStep3FormValues> =
    useCallback(
      async (values) => {
        if (!step1Data || !step2Data) {
          toast.error('Please complete previous steps first');
          setCurrentStep(1);
          return;
        }

        const fullData: ContractorFullFormValues = {
          ...step1Data,
          ...step2Data,
          ...values,
        };

        try {
          if (onSubmit) {
            await onSubmit(fullData);
          } else {
            console.log('Company created with data:', values);
            toast.success('Company created successfully!');
          }
        } catch (error) {
          const errorMessage =
            error instanceof Error
              ? error.message
              : 'An error occurred. Please try again.';
          toast.error(errorMessage);
        }
      },
      [step1Data, step2Data, onSubmit],
    );

  // Navigation handlers
  const goToPreviousStep = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep]);

  // Company code handlers
  const regenerateCode = useCallback(() => {
    const newCode = generateCompanyCode();
    step3Form.setValue('code', newCode);
  }, [step3Form]);

  const handleCopyCode = useCallback(async () => {
    try {
      const code = step3Form.getValues('code');
      await navigator.clipboard.writeText(code);
      setIsCodeCopied(true);
      setTimeout(() => setIsCodeCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  }, [step3Form]);

  return {
    // State
    currentStep,
    step1Data,
    step2Data,
    isCodeCopied,

    // Forms
    step1Form,
    step2Form,
    step3Form,

    // Watched values
    companyRegistrationType,
    watchedCompanyType,

    // Handlers
    handleStep1Submit,
    handleStep2Submit,
    handleStep3Submit,
    goToPreviousStep,
    regenerateCode,
    handleCopyCode,
  };
}
