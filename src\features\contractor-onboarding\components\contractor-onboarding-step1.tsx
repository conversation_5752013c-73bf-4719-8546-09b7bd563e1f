import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { ChevronRight } from 'lucide-react';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { SectionHeader } from '@/components/ui/section-header';
import { useContractorTranslations } from '@/hooks/use-translations';
import { type ContractorStep1FormValues } from '../schemas/contractor-onboarding-schemas';

interface ContractorOnboardingStep1Props {
  form: UseFormReturn<ContractorStep1FormValues>;
  onSubmit: (values: ContractorStep1FormValues) => void;
}

export const ContractorOnboardingStep1 =
  React.memo<ContractorOnboardingStep1Props>(({ form, onSubmit }) => {
    const t = useContractorTranslations();

    return (
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-7">
          <SectionHeader number={1} title={t('onboarding.step1.title')} />

          {/* Full Name */}
          <FormField
            control={form.control}
            name="fullName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {t('onboarding.step1.fullName')}{' '}
                  <span className="text-destructive">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder={t('onboarding.step1.fullNamePlaceholder')}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* IC Number */}
          <FormField
            control={form.control}
            name="icNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {t('onboarding.step1.icNumber')}{' '}
                  <span className="text-destructive">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder={t('onboarding.step1.icNumberPlaceholder')}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Phone Number */}
          <FormField
            control={form.control}
            name="phoneNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {t('onboarding.step1.phoneNumber')}{' '}
                  <span className="text-destructive">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    type="tel"
                    placeholder={t('onboarding.step1.phoneNumberPlaceholder')}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Role Selection */}
          <FormField
            control={form.control}
            name="role"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {t('onboarding.step1.role')}{' '}
                  <span className="text-destructive">*</span>
                </FormLabel>
                <Select onValueChange={field.onChange} value={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue
                        placeholder={t('onboarding.step1.rolePlaceholder')}
                      />
                    </SelectTrigger>
                  </FormControl>{' '}
                  <SelectContent>
                    <SelectItem value="technician">
                      {t('onboarding.step1.technician')}
                    </SelectItem>
                    <SelectItem value="admin">
                      {t('onboarding.step1.admin')}
                    </SelectItem>
                    <SelectItem value="cp">
                      {t('onboarding.step1.cp')}
                    </SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  {t('onboarding.step1.roleHelp')}
                </p>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end pt-6">
            <Button type="submit" className="px-8 py-3">
              {t('onboarding.step1.nextButton')}
              <ChevronRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </form>
      </Form>
    );
  });

ContractorOnboardingStep1.displayName = 'ContractorOnboardingStep1';
