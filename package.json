{"name": "simple-fe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky install", "format": "prettier --write .", "db:types:local": "supabase gen types typescript --local > src/types/database.ts", "db:types:linked": "supabase gen types typescript --linked > src/types/database.ts", "db:migration:new": "supabase migration new", "db:migration:list": "supabase migration list", "db:migration:list:linked": "supabase migration list --linked", "db:push": "supabase db push", "db:link:staging": "supabase link --project-ref $STG_SUPABASE_PROJECT_REF", "db:link:prod": "supabase link --project-ref $PROD_SUPABASE_PROJECT_REF"}, "dependencies": {"@hookform/resolvers": "3.0.0", "@next/env": "^15.3.3", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.8", "@swc/helpers": "^0.5.17", "@tanstack/react-query": "^5.79.0", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.511.0", "next": "15.3.3", "next-intl": "^4.1.0", "postcss": "^8.5.4", "react": "^18", "react-day-picker": "^9.7.0", "react-dom": "^18", "react-hook-form": "7.43.0", "sonner": "^2.0.4", "styled-jsx": "^5.1.7", "tailwind-merge": "^3.3.0", "vaul": "^1.1.2", "zod": "^3.25.42"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/eslintrc": "^3.0.0", "@eslint/js": "^9.28.0", "@tailwindcss/postcss": "^4.1.8", "@tanstack/react-query-devtools": "^5.79.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "commitlint": "^19.8.1", "eslint": "^9.0.0", "eslint-config-next": "15.3.3", "eslint-plugin-react": "^7.37.5", "globals": "^16.2.0", "husky": "^8.0.0", "lint-staged": "^16.1.0", "prettier": "^3.5.3", "rimraf": "^6.0.1", "tailwindcss": "^4.1.8", "tw-animate-css": "^1.3.2", "typescript": "^5", "typescript-eslint": "^8.33.0"}, "lint-staged": {"*.{js,jsx,ts,tsx,json,css,md}": ["prettier --write"], "*.{js,jsx,ts,tsx}": ["eslint --fix"]}}